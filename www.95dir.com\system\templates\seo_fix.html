{#include file="header.html"#}

<div class="main">
    <div class="main-header">
        <h2>SEO修复工具</h2>
        <div class="breadcrumb">
            <a href="index.php">管理首页</a> &raquo; SEO修复工具
        </div>
    </div>

    <div class="main-content">
        <!-- 修复操作面板 -->
        <div class="panel">
            <div class="panel-header">
                <h3>SEO修复操作</h3>
            </div>
            <div class="panel-body">
                <div class="fix-actions">
                    <div class="action-group">
                        <h4>Meta标签优化</h4>
                        <p>清理重复的meta标签，确保每个页面只有一个robots、viewport等标签</p>
                        <a href="?act=clean_meta" class="btn btn-primary">清理重复Meta标签</a>
                    </div>

                    <div class="action-group">
                        <h4>Canonical链接修复</h4>
                        <p>检查并修复页面的canonical链接设置，特别是分页页面</p>
                        <a href="?act=fix_canonical" class="btn btn-primary">修复Canonical链接</a>
                    </div>

                    <div class="action-group">
                        <h4>生成网站地图</h4>
                        <p>生成最新的sitemap.xml文件，提交给搜索引擎</p>
                        <a href="?act=generate_sitemap" class="btn btn-primary">生成Sitemap</a>
                    </div>

                    <div class="action-group">
                        <h4>检查标题重复</h4>
                        <p>检查网站和文章中是否有重复的标题</p>
                        <a href="?act=check_titles" class="btn btn-warning">检查重复标题</a>
                    </div>

                    <div class="action-group">
                        <h4>图片Alt属性</h4>
                        <p>检查模板文件中缺少alt属性的图片</p>
                        <a href="?act=fix_images" class="btn btn-warning">检查图片Alt</a>
                    </div>

                    <div class="action-group">
                        <h4>页面加载速度</h4>
                        <p>测试主要页面的加载速度</p>
                        <a href="?act=check_speed" class="btn btn-info">测试加载速度</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 修复结果显示 -->
        {#if $fix_type#}
        <div class="panel">
            <div class="panel-header">
                <h3>修复结果</h3>
            </div>
            <div class="panel-body">
                {#if $fix_type == 'meta'#}
                    {#if $fixed_files#}
                    <div class="success-message">
                        <h4>✓ Meta标签清理完成</h4>
                        <p>已修复以下文件：</p>
                        <ul>
                            {#foreach from=$fixed_files item=file#}
                            <li>{#$file#}</li>
                            {#/foreach#}
                        </ul>
                    </div>
                    {#else#}
                    <div class="info-message">
                        <h4>ℹ 未发现需要修复的Meta标签</h4>
                        <p>所有模板文件的Meta标签设置正常。</p>
                    </div>
                    {#/if#}
                {#/if#}

                {#if $fix_type == 'canonical'#}
                    <div class="info-message">
                        <h4>Canonical链接检查结果</h4>
                        <ul>
                            {#foreach from=$fixed_issues item=issue#}
                            <li>{#$issue#}</li>
                            {#/foreach#}
                        </ul>
                    </div>
                {#/if#}

                {#if $fix_type == 'sitemap'#}
                    {#if $sitemap_generated#}
                    <div class="success-message">
                        <h4>✓ Sitemap生成成功</h4>
                        <p>文件大小：{#$sitemap_size#} 字节</p>
                        <p>包含URL数量：{#$sitemap_urls#} 个</p>
                        <p>文件位置：/sitemap.xml</p>
                    </div>
                    {#else#}
                    <div class="error-message">
                        <h4>✗ Sitemap生成失败</h4>
                        <p>{#$sitemap_error#}</p>
                    </div>
                    {#/if#}
                {#/if#}

                {#if $fix_type == 'titles'#}
                    {#if $title_issues#}
                    <div class="warning-message">
                        <h4>⚠ 发现重复标题</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>类型</th>
                                    <th>标题</th>
                                    <th>重复次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                {#foreach from=$title_issues item=issue#}
                                <tr>
                                    <td>{#$issue.type#}</td>
                                    <td>{#$issue.title#}</td>
                                    <td>{#$issue.count#}</td>
                                </tr>
                                {#/foreach#}
                            </tbody>
                        </table>
                    </div>
                    {#else#}
                    <div class="success-message">
                        <h4>✓ 未发现重复标题</h4>
                    </div>
                    {#/if#}
                {#/if#}

                {#if $fix_type == 'images'#}
                    {#if $image_fixes#}
                    <div class="warning-message">
                        <h4>⚠ 发现缺少Alt属性的图片</h4>
                        {#foreach from=$image_fixes item=fix#}
                        <div class="image-fix-item">
                            <h5>{#$fix.file#} - {#$fix.count#}个图片缺少alt属性</h5>
                            <div class="image-list">
                                {#foreach from=$fix.images item=img#}
                                <code>{#$img#}</code><br>
                                {#/foreach#}
                            </div>
                        </div>
                        {#/foreach#}
                    </div>
                    {#else#}
                    <div class="success-message">
                        <h4>✓ 所有图片都有Alt属性</h4>
                    </div>
                    {#/if#}
                {#/if#}

                {#if $fix_type == 'speed'#}
                    <div class="info-message">
                        <h4>页面加载速度测试结果</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>页面</th>
                                    <th>加载时间</th>
                                    <th>内容大小</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {#foreach from=$speed_tests item=test#}
                                <tr class="{#$test.status#}">
                                    <td>{#$test.name#}</td>
                                    <td>{#$test.load_time#}ms</td>
                                    <td>{#$test.content_size#} bytes</td>
                                    <td>
                                        {#if $test.status == 'good'#}✓ 良好
                                        {#elseif $test.status == 'warning'#}⚠ 一般
                                        {#else#}✗ 较慢{#/if#}
                                    </td>
                                </tr>
                                {#/foreach#}
                            </tbody>
                        </table>
                    </div>
                {#/if#}
            </div>
        </div>
        {#/if#}
    </div>
</div>

<style>
.fix-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.action-group {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.action-group h4 {
    margin-top: 0;
    color: #333;
}

.action-group p {
    color: #666;
    margin-bottom: 15px;
}

.success-message {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.warning-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.error-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.info-message {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.image-fix-item {
    margin-bottom: 20px;
}

.image-list {
    margin-top: 10px;
    max-height: 200px;
    overflow-y: auto;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.table th, .table td {
    padding: 8px 12px;
    border: 1px solid #ddd;
    text-align: left;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.table tr.good {
    background-color: #d4edda;
}

.table tr.warning {
    background-color: #fff3cd;
}

.table tr.slow {
    background-color: #f8d7da;
}
</style>

{#include file="footer.html"#}
