<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="index,follow" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
{#if $cate_id > 0#}
<meta name="apple-mobile-web-app-title" content="{#$cate_name#}文章 - {#$options.site_name#}" />
{#if $smarty.get.page && $smarty.get.page > 1#}
<link rel="canonical" href="{#$site_url#}?mod=article&cid={#$cate_id#}&page={#$smarty.get.page#}" />
<link rel="prev" href="{#if $smarty.get.page == 2#}{#$site_url#}?mod=article&cid={#$cate_id#}{#else#}{#$site_url#}?mod=article&cid={#$cate_id#}&page={#$smarty.get.page-1#}{#/if#}" />
{#if $smarty.get.page < $total_pages#}<link rel="next" href="{#$site_url#}?mod=article&cid={#$cate_id#}&page={#$smarty.get.page+1#}" />{#/if#}
<link rel="alternate" media="only screen and (max-width: 640px)" href="{#$site_url#}?mod=article&cid={#$cate_id#}&page={#$smarty.get.page#}" />
{#else#}
<link rel="canonical" href="{#$site_url#}?mod=article&cid={#$cate_id#}" />
{#if $total_pages > 1#}<link rel="next" href="{#$site_url#}?mod=article&cid={#$cate_id#}&page=2" />{#/if#}
<link rel="alternate" media="only screen and (max-width: 640px)" href="{#$site_url#}?mod=article&cid={#$cate_id#}" />
{#/if#}
{#else#}
<meta name="apple-mobile-web-app-title" content="文章列表 - {#$options.site_name#}" />
{#if $smarty.get.page && $smarty.get.page > 1#}
<link rel="canonical" href="{#$site_url#}?mod=article&page={#$smarty.get.page#}" />
<link rel="prev" href="{#if $smarty.get.page == 2#}{#$site_url#}?mod=article{#else#}{#$site_url#}?mod=article&page={#$smarty.get.page-1#}{#/if#}" />
{#if $smarty.get.page < $total_pages#}<link rel="next" href="{#$site_url#}?mod=article&page={#$smarty.get.page+1#}" />{#/if#}
<link rel="alternate" media="only screen and (max-width: 640px)" href="{#$site_url#}?mod=article&page={#$smarty.get.page#}" />
{#else#}
<link rel="canonical" href="{#$site_url#}?mod=article" />
{#if $total_pages > 1#}<link rel="next" href="{#$site_url#}?mod=article&page=2" />{#/if#}
<link rel="alternate" media="only screen and (max-width: 640px)" href="{#$site_url#}?mod=article" />
{#/if#}
{#/if#}
<meta name="theme-color" content="#28a745" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="subcate" class="clearfix">
            	<h3>{#$cate_name#}</h3>
                <ul class="scatelist">
                	{#foreach from=$categories item=sub#}
                    {#if $sub.cate_mod != 'webdir'#}
                    <li><a href="{#$sub.cate_link#}">{#$sub.cate_name#}</a><em>({#$sub.cate_postcount#})</em></li>
                    {#/if#}
                    {#/foreach#}
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="listbox" class="clearfix">
            	<h2>{#$cate_name#}</h2>
<ul class="artlist">
					{#foreach from=$articles item=a name=list#}
                	<li><h3><a href="{#$a.art_link#}" title="{#$a.art_title#}">{#$a.art_title#}{#if $a.is_today#}<span class="new-icon">new</span>{#/if#}</a></h3><p>{#$a.art_intro#}</p><cite>{#$a.art_ctime#}</cite></li>
                	{#foreachelse#}
                	<li>该目录下无任何内容！</li>
                	{#/foreach#}
				</ul>



            	<div class="showpage">{#$showpage#}</div>
            </div>
        </div>
        <div id="mainbox-right">
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>
            <div class="blank10"></div>-->
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li><a href="{#$art.art_link#}">{#$art.art_title#}{#if $art.is_today#}<span class="new-icon">new</span>{#/if#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}" /></a><strong><a href="{#$quick.web_link#}" title="{#$best.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>
</body>
</html>