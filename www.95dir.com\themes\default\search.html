<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="index,follow" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="apple-mobile-web-app-title" content="搜索结果 - {#$options.site_name#}" />
<meta name="theme-color" content="#17a2b8" />
{#if $smarty.get.page && $smarty.get.page > 1#}
<link rel="canonical" href="{#$site_url#}?mod=search&type={#$search_type#}&query={#$keyword|urlencode#}&page={#$smarty.get.page#}" />
<link rel="prev" href="{#if $smarty.get.page == 2#}{#$site_url#}?mod=search&type={#$search_type#}&query={#$keyword|urlencode#}{#else#}{#$site_url#}?mod=search&type={#$search_type#}&query={#$keyword|urlencode#}&page={#$smarty.get.page-1#}{#/if#}" />
{#if $smarty.get.page < $total_pages#}<link rel="next" href="{#$site_url#}?mod=search&type={#$search_type#}&query={#$keyword|urlencode#}&page={#$smarty.get.page+1#}" />{#/if#}
{#else#}
<link rel="canonical" href="{#$site_url#}?mod=search&type={#$search_type#}&query={#$keyword|urlencode#}" />
{#if $total_pages > 1#}<link rel="next" href="{#$site_url#}?mod=search&type={#$search_type#}&query={#$keyword|urlencode#}&page=2" />{#/if#}
{#/if#}
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}public/css/logo-preview.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="{#$site_root#}public/js/logo-optimizer.js"></script>
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
            <div id="listbox" class="clearfix">
            	<h2>搜索结果{#if $keyword#} - "{#$keyword#}"{#/if#} {#if $total > 0#}(共找到 {#$total#} 条结果){#/if#}</h2>

            	{#if $search_type == 'article'#}
            	<!-- 文章搜索结果 -->
            	<ul class="sitelist">
					{#foreach from=$articles item=art name=list#}
                	<li>
                		<div class="info">
                			<h3><a href="{#$art.art_link#}" title="{#$art.art_title#}">{#$art.art_title#}</a></h3>
                			<p>{#$art.art_intro#}</p>
                			<address>
                				发布时间：{#$art.art_ctime#} |
                				分类：<a href="{#$art.cate_link#}">{#$art.cate_name#}</a>
                				{#if $art.art_tags#} | 标签：
                				{#foreach from=$art.art_tags item=tag name=tags#}
                					<a href="{#$tag.tag_link#}">{#$tag.tag_name#}</a>{#if !$smarty.foreach.tags.last#}、{#/if#}
                				{#/foreach#}
                				{#/if#}
                			</address>
                		</div>
                	</li>
                	{#foreachelse#}
                	<li>没有找到相关文章！</li>
                	{#/foreach#}
				</ul>
				{#else#}
				<!-- 网站搜索结果 -->
            	<ul class="sitelist">
					{#foreach from=$websites item=w name=list#}
                	<li><a href="{#$w.web_link#}"><img src="{#$w.web_pic#}" width="100" height="80" alt="{#$w.web_name#}" class="thumb" /></a><div class="info"><h3><a href="{#$w.web_link#}" title="{#$w.web_name#}">{#$w.web_name#}</a> {#if $w.web_ispay == 1#}<img src="{#$site_root#}public/images/attr/audit.gif" border="0">{#/if#} {#if $w.web_istop == 1#}<img src="{#$site_root#}public/images/attr/top.gif" border="0">{#/if#} {#if $w.web_isbest == 1#}<img src="{#$site_root#}public/images/attr/best.gif" border="0">{#/if#}</h3><p>{#$w.web_intro#}</p><address><a href="{#$w.web_furl#}" target="_blank" class="visit" onClick="clickout({#$w.web_id#})">{#$w.web_url#}</a> - {#$w.web_ctime#} - <a href="javascript:;" class="addfav" onClick="addfav({#$w.web_id#})" title="点击收藏">收藏</a></address></div></li>
                	{#foreachelse#}
                	<li>没有找到相关网站！</li>
                	{#/foreach#}
				</ul>
				{#/if#}
            	<div class="showpage">{#$showpage#}</div>
            </div>
        </div>
        <div id="mainbox-right">
        	<div class="ad250x250">{#get_adcode(7)#}</div>
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li><a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>
</body>
</html>