<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagetitle = 'SEO修复工具';
$tempfile = 'seo_fix.html';

/** 清理重复的meta标签 */
if ($action == 'clean_meta') {
    $fixed_files = array();
    $template_dir = IWEBDIR_ROOT . '/themes/default/';
    
    $template_files = array(
        'index.html',
        'webdir.html', 
        'article.html',
        'siteinfo.html',
        'artinfo.html',
        'search.html',
        'category.html'
    );
    
    foreach ($template_files as $file) {
        $file_path = $template_dir . $file;
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            $original_content = $content;
            
            // 移除重复的meta标签
            $content = preg_replace('/(<meta[^>]*name=["\']robots["\'][^>]*>\s*)+/i', '$1', $content);
            $content = preg_replace('/(<meta[^>]*name=["\']viewport["\'][^>]*>\s*)+/i', '$1', $content);
            $content = preg_replace('/(<meta[^>]*name=["\']keywords["\'][^>]*>\s*)+/i', '$1', $content);
            $content = preg_replace('/(<meta[^>]*name=["\']description["\'][^>]*>\s*)+/i', '$1', $content);
            
            if ($content !== $original_content) {
                file_put_contents($file_path, $content);
                $fixed_files[] = $file;
            }
        }
    }
    
    $smarty->assign('fixed_files', $fixed_files);
    $smarty->assign('fix_type', 'meta');
}

/** 修复canonical链接 */
if ($action == 'fix_canonical') {
    $fixed_issues = array();
    
    // 检查并修复webdir.html的canonical设置
    $webdir_file = IWEBDIR_ROOT . '/themes/default/webdir.html';
    if (file_exists($webdir_file)) {
        $content = file_get_contents($webdir_file);
        
        // 检查是否已经有正确的分页canonical逻辑
        if (strpos($content, 'smarty.get.page') === false) {
            $fixed_issues[] = 'webdir.html需要手动添加分页canonical逻辑';
        } else {
            $fixed_issues[] = 'webdir.html的canonical设置已优化';
        }
    }
    
    // 检查并修复article.html的canonical设置
    $article_file = IWEBDIR_ROOT . '/themes/default/article.html';
    if (file_exists($article_file)) {
        $content = file_get_contents($article_file);
        
        if (strpos($content, 'smarty.get.page') === false) {
            $fixed_issues[] = 'article.html需要手动添加分页canonical逻辑';
        } else {
            $fixed_issues[] = 'article.html的canonical设置已优化';
        }
    }
    
    $smarty->assign('fixed_issues', $fixed_issues);
    $smarty->assign('fix_type', 'canonical');
}

/** 生成sitemap */
if ($action == 'generate_sitemap') {
    $sitemap_url = $options['site_url'] . '?mod=sitemap';
    $sitemap_content = get_url_content($sitemap_url);
    
    if (!empty($sitemap_content)) {
        $sitemap_file = IWEBDIR_ROOT . '/sitemap.xml';
        file_put_contents($sitemap_file, $sitemap_content);
        
        $smarty->assign('sitemap_generated', true);
        $smarty->assign('sitemap_size', strlen($sitemap_content));
        $smarty->assign('sitemap_urls', substr_count($sitemap_content, '<url>'));
    } else {
        $smarty->assign('sitemap_error', '无法生成sitemap');
    }
    
    $smarty->assign('fix_type', 'sitemap');
}

/** 检查页面标题重复 */
if ($action == 'check_titles') {
    $title_issues = array();
    
    // 检查网站数据中的重复标题
    $query = $DB->query("SELECT web_name, COUNT(*) as count FROM " . $DB->table('websites') . " WHERE web_status=3 GROUP BY web_name HAVING count > 1 ORDER BY count DESC LIMIT 20");
    while ($row = $DB->fetch_array($query)) {
        $title_issues[] = array(
            'type' => '网站标题重复',
            'title' => $row['web_name'],
            'count' => $row['count']
        );
    }
    
    // 检查文章标题重复
    $query = $DB->query("SELECT art_title, COUNT(*) as count FROM " . $DB->table('articles') . " WHERE art_status=3 GROUP BY art_title HAVING count > 1 ORDER BY count DESC LIMIT 20");
    while ($row = $DB->fetch_array($query)) {
        $title_issues[] = array(
            'type' => '文章标题重复',
            'title' => $row['art_title'],
            'count' => $row['count']
        );
    }
    
    $smarty->assign('title_issues', $title_issues);
    $smarty->assign('fix_type', 'titles');
}

/** 优化图片alt属性 */
if ($action == 'fix_images') {
    $image_fixes = array();
    
    // 检查模板文件中的图片
    $template_dir = IWEBDIR_ROOT . '/themes/default/';
    $template_files = glob($template_dir . '*.html');
    
    foreach ($template_files as $file) {
        $content = file_get_contents($file);
        $filename = basename($file);
        
        // 查找没有alt属性的img标签
        if (preg_match_all('/<img(?![^>]*alt=)[^>]*>/i', $content, $matches)) {
            $image_fixes[] = array(
                'file' => $filename,
                'count' => count($matches[0]),
                'images' => $matches[0]
            );
        }
    }
    
    $smarty->assign('image_fixes', $image_fixes);
    $smarty->assign('fix_type', 'images');
}

/** 检查页面加载速度 */
if ($action == 'check_speed') {
    $speed_tests = array();
    
    $test_urls = array(
        '首页' => $options['site_url'],
        '网站目录' => $options['site_url'] . '?mod=webdir',
        '文章列表' => $options['site_url'] . '?mod=article',
        '搜索页面' => $options['site_url'] . '?mod=search&type=name&query=test'
    );
    
    foreach ($test_urls as $name => $url) {
        $start_time = microtime(true);
        $content = get_url_content($url);
        $end_time = microtime(true);
        
        $load_time = round(($end_time - $start_time) * 1000, 2);
        $content_size = strlen($content);
        
        $speed_tests[] = array(
            'name' => $name,
            'url' => $url,
            'load_time' => $load_time,
            'content_size' => $content_size,
            'status' => $load_time < 1000 ? 'good' : ($load_time < 3000 ? 'warning' : 'slow')
        );
    }
    
    $smarty->assign('speed_tests', $speed_tests);
    $smarty->assign('fix_type', 'speed');
}

smarty_output($tempfile);
?>
