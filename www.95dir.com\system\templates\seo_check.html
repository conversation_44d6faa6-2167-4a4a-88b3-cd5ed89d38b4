{#include file="header.html"#}

<div class="main">
    <div class="main-header">
        <h2>SEO检查工具</h2>
        <div class="breadcrumb">
            <a href="index.php">管理首页</a> &raquo; SEO检查工具
        </div>
    </div>

    <div class="main-content">
        <!-- 单页面SEO检查 -->
        <div class="panel">
            <div class="panel-header">
                <h3>单页面SEO检查</h3>
            </div>
            <div class="panel-body">
                <form method="post" action="?act=check">
                    <div class="form-group">
                        <label>页面URL：</label>
                        <input type="text" name="url" value="{#$check_url#}" placeholder="请输入要检查的页面URL" style="width: 500px;" />
                        <input type="submit" value="检查SEO" class="btn btn-primary" />
                    </div>
                </form>

                {#if $seo_issues || $seo_suggestions#}
                <div class="seo-results">
                    <h4>检查结果</h4>
                    <div class="result-info">
                        <p><strong>检查URL：</strong>{#$check_url#}</p>
                        <p><strong>页面标题：</strong>{#$page_title#}</p>
                        <p><strong>页面描述：</strong>{#$page_description#}</p>
                        <p><strong>页面关键词：</strong>{#$page_keywords#}</p>
                        <p><strong>图片统计：</strong>共{#$img_count#}张图片，{#$img_no_alt#}张缺少alt属性</p>
                        <p><strong>链接统计：</strong>内部链接{#$internal_links#}个，外部链接{#$external_links#}个</p>
                    </div>

                    {#if $seo_issues#}
                    <div class="seo-issues">
                        <h5 style="color: #d9534f;">发现的SEO问题：</h5>
                        <ul>
                            {#foreach from=$seo_issues item=issue#}
                            <li style="color: #d9534f;">• {#$issue#}</li>
                            {#/foreach#}
                        </ul>
                    </div>
                    {#/if#}

                    {#if $seo_suggestions#}
                    <div class="seo-suggestions">
                        <h5 style="color: #5cb85c;">建议和提示：</h5>
                        <ul>
                            {#foreach from=$seo_suggestions item=suggestion#}
                            <li style="color: #5cb85c;">• {#$suggestion#}</li>
                            {#/foreach#}
                        </ul>
                    </div>
                    {#/if#}
                </div>
                {#/if#}
            </div>
        </div>

        <!-- 分页SEO检查 -->
        <div class="panel">
            <div class="panel-header">
                <h3>分页页面SEO检查</h3>
            </div>
            <div class="panel-body">
                <form method="post" action="?act=check_pagination">
                    <div class="form-group">
                        <label>基础URL：</label>
                        <input type="text" name="base_url" placeholder="例如：{#$site_url#}?mod=webdir&cid=1" style="width: 400px;" />
                    </div>
                    <div class="form-group">
                        <label>检查页数：</label>
                        <input type="number" name="max_pages" value="5" min="1" max="10" />
                        <span class="help-text">最多检查10页</span>
                    </div>
                    <div class="form-group">
                        <input type="submit" value="检查分页SEO" class="btn btn-primary" />
                    </div>
                </form>

                {#if $pagination_issues#}
                <div class="pagination-results">
                    <h4>分页检查结果（检查了{#$checked_pages#}页）</h4>
                    {#if $pagination_issues|@count > 0#}
                    <div class="pagination-issues">
                        <h5 style="color: #d9534f;">发现的分页SEO问题：</h5>
                        <ul>
                            {#foreach from=$pagination_issues item=issue#}
                            <li style="color: #d9534f;">• {#$issue#}</li>
                            {#/foreach#}
                        </ul>
                    </div>
                    {#else#}
                    <div style="color: #5cb85c;">
                        <h5>✓ 分页SEO设置正常！</h5>
                    </div>
                    {#/if#}
                </div>
                {#/if#}
            </div>
        </div>

        <!-- SEO优化建议 -->
        <div class="panel">
            <div class="panel-header">
                <h3>SEO优化建议</h3>
            </div>
            <div class="panel-body">
                <div class="seo-tips">
                    <h4>常见SEO问题及解决方案：</h4>
                    <ul>
                        <li><strong>noindex问题：</strong>检查页面是否设置了&lt;meta name="robots" content="noindex"&gt;，如果不需要可以移除或改为"index,follow"</li>
                        <li><strong>重复内容：</strong>确保每个页面都有唯一的标题和描述，分页页面应包含页码信息</li>
                        <li><strong>canonical链接：</strong>每个页面都应该有正确的canonical链接，分页页面的canonical应指向当前页面</li>
                        <li><strong>标题优化：</strong>页面标题应在10-60字符之间，包含关键词且具有描述性</li>
                        <li><strong>描述优化：</strong>Meta描述应在50-160字符之间，准确描述页面内容</li>
                        <li><strong>图片优化：</strong>所有图片都应该有alt属性，提高可访问性和SEO效果</li>
                    </ul>

                    <h4>分页SEO最佳实践：</h4>
                    <ul>
                        <li>第一页canonical指向不带page参数的URL</li>
                        <li>其他页面canonical指向带正确page参数的URL</li>
                        <li>使用rel="prev"和rel="next"标签建立分页关系</li>
                        <li>分页页面标题包含"第X页"信息</li>
                        <li>分页页面描述也应包含页码信息</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.seo-results, .pagination-results {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.result-info {
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.seo-issues, .seo-suggestions, .pagination-issues {
    margin-top: 15px;
}

.seo-issues ul, .seo-suggestions ul, .pagination-issues ul {
    margin: 10px 0;
    padding-left: 20px;
}

.seo-tips {
    background-color: #f0f8ff;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.seo-tips h4 {
    color: #007bff;
    margin-top: 20px;
    margin-bottom: 10px;
}

.seo-tips h4:first-child {
    margin-top: 0;
}

.seo-tips ul {
    margin: 10px 0;
    padding-left: 20px;
}

.seo-tips li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.help-text {
    color: #666;
    font-size: 12px;
    margin-left: 10px;
}
</style>

{#include file="footer.html"#}
