<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagetitle = 'SEO检查工具';
$tempfile = 'seo_check.html';

/** SEO检查功能 */
if ($action == 'check') {
    $url = trim($_POST['url']);
    if (empty($url)) {
        msgbox('请输入要检查的URL！');
    }
    
    // 检查URL是否为本站URL
    $site_domain = parse_url($options['site_url'], PHP_URL_HOST);
    $check_domain = parse_url($url, PHP_URL_HOST);
    
    if ($check_domain !== $site_domain) {
        msgbox('只能检查本站URL！');
    }
    
    $seo_issues = array();
    $seo_suggestions = array();
    
    // 获取页面内容
    $content = get_url_content($url);
    if (empty($content)) {
        msgbox('无法获取页面内容！');
    }
    
    // 检查标题
    if (preg_match('/<title>(.*?)<\/title>/is', $content, $matches)) {
        $title = trim($matches[1]);
        if (empty($title)) {
            $seo_issues[] = '页面标题为空';
        } elseif (mb_strlen($title, 'UTF-8') > 60) {
            $seo_issues[] = '页面标题过长（超过60字符）';
        } elseif (mb_strlen($title, 'UTF-8') < 10) {
            $seo_issues[] = '页面标题过短（少于10字符）';
        }
    } else {
        $seo_issues[] = '缺少页面标题';
    }
    
    // 检查meta描述
    if (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']*)["\'][^>]*>/is', $content, $matches)) {
        $description = trim($matches[1]);
        if (empty($description)) {
            $seo_issues[] = 'Meta描述为空';
        } elseif (mb_strlen($description, 'UTF-8') > 160) {
            $seo_issues[] = 'Meta描述过长（超过160字符）';
        } elseif (mb_strlen($description, 'UTF-8') < 50) {
            $seo_issues[] = 'Meta描述过短（少于50字符）';
        }
    } else {
        $seo_issues[] = '缺少Meta描述';
    }
    
    // 检查meta关键词
    if (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']*)["\'][^>]*>/is', $content, $matches)) {
        $keywords = trim($matches[1]);
        if (empty($keywords)) {
            $seo_issues[] = 'Meta关键词为空';
        }
    } else {
        $seo_issues[] = '缺少Meta关键词';
    }
    
    // 检查robots标签
    if (preg_match('/<meta\s+name=["\']robots["\']\s+content=["\']([^"\']*)["\'][^>]*>/is', $content, $matches)) {
        $robots = strtolower(trim($matches[1]));
        if (strpos($robots, 'noindex') !== false) {
            $seo_issues[] = '页面设置了noindex，搜索引擎不会索引此页面';
        }
        if (strpos($robots, 'nofollow') !== false) {
            $seo_issues[] = '页面设置了nofollow，搜索引擎不会跟踪页面链接';
        }
    }
    
    // 检查canonical链接
    if (preg_match('/<link\s+rel=["\']canonical["\']\s+href=["\']([^"\']*)["\'][^>]*>/is', $content, $matches)) {
        $canonical = trim($matches[1]);
        if ($canonical !== $url) {
            $seo_suggestions[] = 'Canonical链接指向其他页面：' . $canonical;
        }
    } else {
        $seo_issues[] = '缺少Canonical链接';
    }
    
    // 检查H1标签
    $h1_count = preg_match_all('/<h1[^>]*>(.*?)<\/h1>/is', $content, $h1_matches);
    if ($h1_count == 0) {
        $seo_issues[] = '缺少H1标签';
    } elseif ($h1_count > 1) {
        $seo_issues[] = '页面有多个H1标签（' . $h1_count . '个）';
    }
    
    // 检查图片alt属性
    $img_count = preg_match_all('/<img[^>]*>/is', $content, $img_matches);
    $img_no_alt = preg_match_all('/<img(?![^>]*alt=)[^>]*>/is', $content);
    if ($img_no_alt > 0) {
        $seo_issues[] = '有' . $img_no_alt . '张图片缺少alt属性';
    }
    
    // 检查内部链接
    $internal_links = preg_match_all('/<a[^>]*href=["\']([^"\']*)["\'][^>]*>/is', $content, $link_matches);
    $external_links = 0;
    foreach ($link_matches[1] as $link) {
        if (strpos($link, 'http') === 0 && strpos($link, $site_domain) === false) {
            $external_links++;
        }
    }
    
    // 生成建议
    if (empty($seo_issues)) {
        $seo_suggestions[] = '页面SEO设置良好！';
    } else {
        $seo_suggestions[] = '发现 ' . count($seo_issues) . ' 个SEO问题需要修复';
    }
    
    $smarty->assign('check_url', $url);
    $smarty->assign('seo_issues', $seo_issues);
    $smarty->assign('seo_suggestions', $seo_suggestions);
    $smarty->assign('page_title', isset($title) ? $title : '');
    $smarty->assign('page_description', isset($description) ? $description : '');
    $smarty->assign('page_keywords', isset($keywords) ? $keywords : '');
    $smarty->assign('img_count', $img_count);
    $smarty->assign('img_no_alt', $img_no_alt);
    $smarty->assign('internal_links', $internal_links);
    $smarty->assign('external_links', $external_links);
}

/** 批量检查分页页面 */
if ($action == 'check_pagination') {
    $base_url = trim($_POST['base_url']);
    $max_pages = intval($_POST['max_pages']);
    
    if (empty($base_url) || $max_pages < 1) {
        msgbox('请输入有效的基础URL和页数！');
    }
    
    $pagination_issues = array();
    
    for ($page = 1; $page <= min($max_pages, 10); $page++) {
        $check_url = $base_url . ($page > 1 ? '&page=' . $page : '');
        $content = get_url_content($check_url);
        
        if (!empty($content)) {
            // 检查canonical
            if (preg_match('/<link\s+rel=["\']canonical["\']\s+href=["\']([^"\']*)["\'][^>]*>/is', $content, $matches)) {
                $canonical = trim($matches[1]);
                $expected_canonical = $base_url . ($page > 1 ? '&page=' . $page : '');
                
                if ($canonical !== $expected_canonical) {
                    $pagination_issues[] = "第{$page}页canonical错误：{$canonical}，应为：{$expected_canonical}";
                }
            } else {
                $pagination_issues[] = "第{$page}页缺少canonical链接";
            }
            
            // 检查标题
            if (preg_match('/<title>(.*?)<\/title>/is', $content, $matches)) {
                $title = trim($matches[1]);
                if ($page > 1 && strpos($title, '第' . $page . '页') === false) {
                    $pagination_issues[] = "第{$page}页标题未包含页码信息";
                }
            }
        }
    }
    
    $smarty->assign('pagination_issues', $pagination_issues);
    $smarty->assign('checked_pages', min($max_pages, 10));
}

smarty_output($tempfile);
?>
